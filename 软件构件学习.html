<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件构件分类 - 交互式学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: bold;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .section-title {
            font-size: 1.8rem;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .components-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .component-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: scale(0.9);
            opacity: 0;
        }

        .component-card:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .component-card.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            transform: scale(1.1);
        }

        .component-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .component-desc {
            font-size: 1rem;
            line-height: 1.6;
            text-align: center;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        #animationCanvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            max-width: 100%;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .option-btn {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border: none;
            border-radius: 15px;
            padding: 20px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #333;
            font-weight: 500;
        }

        .option-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .option-btn.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .option-btn.wrong {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .explanation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
        }

        .start-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2rem;
            border-radius: 50px;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🧩 软件构件分类学习</h1>
        
        <div class="question-card">
            <div class="question-text">
                按照外部形态，构成一个软件系统的构件可以分为五类，其中，<span class="highlight">（ ）是指可以进行版本替换并增加构件新功能</span>。
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎯 让我们先理解什么是软件构件</h2>
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
                <button class="start-btn" onclick="startAnimation()">开始动画演示</button>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 五种构件类型详解</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="components-container" id="componentsContainer">
                <!-- 构件卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">🎮 现在来答题吧！</h2>
            <div class="question-text">
                根据题目描述："可以进行版本替换并增加构件新功能"，这是哪种构件的特点？
            </div>
            <div class="options-container">
                <button class="option-btn" onclick="selectOption(this, 'A')">A. 装配的构件</button>
                <button class="option-btn" onclick="selectOption(this, 'B')">B. 可修改的构件</button>
                <button class="option-btn" onclick="selectOption(this, 'C')">C. 有限制的构件</button>
                <button class="option-btn" onclick="selectOption(this, 'D')">D. 适应性构件</button>
            </div>
            <div class="explanation" id="explanation"></div>
        </div>
    </div>

    <script>
        // 构件数据
        const components = [
            {
                title: "🔒 独立而成熟的构件",
                desc: "经过多次检验，隐藏所有接口，只需用规定命令使用。如：数据库管理系统、操作系统",
                color: ["#ff9a9e", "#fecfef"]
            },
            {
                title: "⚠️ 有限制的构件", 
                desc: "提供接口，但有使用条件，装配时可能产生冲突。如：面向对象语言的基础类库",
                color: ["#a18cd1", "#fbc2eb"]
            },
            {
                title: "🔄 适应性构件",
                desc: "经过包装处理，解决了不兼容问题，可直接在各种环境使用。如：ActiveX",
                color: ["#ffecd2", "#fcb69f"]
            },
            {
                title: "🔧 装配的构件",
                desc: "已装配在系统不同层次，用胶水代码连接使用。如：大多数商业软件产品",
                color: ["#a8edea", "#fed6e3"]
            },
            {
                title: "✏️ 可修改的构件",
                desc: "可以进行版本替换，通过重新包装或写接口实现替换，能增加新功能",
                color: ["#4facfe", "#00f2fe"]
            }
        ];

        let currentStep = 0;
        let canvas, ctx;

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('animationCanvas');
            ctx = canvas.getContext('2d');
            
            // 初始动画
            gsap.to('.title', {duration: 1, opacity: 1, y: 0, ease: 'back.out(1.7)'});
            gsap.to('.question-card', {duration: 1, opacity: 1, y: 0, delay: 0.3, ease: 'back.out(1.7)'});
            gsap.to('.learning-section', {duration: 1, opacity: 1, y: 0, delay: 0.6, ease: 'back.out(1.7)', stagger: 0.2});
            
            createComponentCards();
        };

        // 创建构件卡片
        function createComponentCards() {
            const container = document.getElementById('componentsContainer');
            
            components.forEach((component, index) => {
                const card = document.createElement('div');
                card.className = 'component-card';
                card.style.background = `linear-gradient(135deg, ${component.color[0]} 0%, ${component.color[1]} 100%)`;
                card.innerHTML = `
                    <div class="component-title">${component.title}</div>
                    <div class="component-desc">${component.desc}</div>
                `;
                
                card.addEventListener('click', () => showComponentDetail(index));
                container.appendChild(card);
                
                // 延迟显示动画
                gsap.to(card, {
                    duration: 0.8,
                    opacity: 1,
                    scale: 1,
                    delay: index * 0.2,
                    ease: 'back.out(1.7)'
                });
            });
        }

        // 显示构件详情
        function showComponentDetail(index) {
            const cards = document.querySelectorAll('.component-card');
            cards.forEach((card, i) => {
                if (i === index) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
            });
            
            // 更新进度条
            const progress = ((index + 1) / components.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            
            // 画布动画演示
            animateComponent(index);
        }

        // 开始动画
        function startAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制软件系统概念
            drawSoftwareSystem();
        }

        // 绘制软件系统
        function drawSoftwareSystem() {
            ctx.fillStyle = '#667eea';
            ctx.font = '24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('软件系统 = 构件的集合', canvas.width/2, 50);
            
            // 绘制构件示意图
            const colors = ['#ff9a9e', '#a18cd1', '#ffecd2', '#a8edea', '#4facfe'];
            
            for (let i = 0; i < 5; i++) {
                const x = 100 + i * 120;
                const y = 150;
                
                // 绘制构件方块
                ctx.fillStyle = colors[i];
                ctx.fillRect(x, y, 80, 80);
                
                // 添加标签
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(`构件${i+1}`, x + 40, y + 100);
                
                // 动画效果
                gsap.from(`#animationCanvas`, {
                    duration: 0.5,
                    delay: i * 0.2,
                    ease: 'bounce.out'
                });
            }
            
            // 绘制连接线
            ctx.strokeStyle = '#764ba2';
            ctx.lineWidth = 2;
            ctx.beginPath();
            for (let i = 0; i < 4; i++) {
                const x1 = 180 + i * 120;
                const x2 = 220 + i * 120;
                ctx.moveTo(x1, 190);
                ctx.lineTo(x2, 190);
            }
            ctx.stroke();
        }

        // 构件动画演示
        function animateComponent(index) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const component = components[index];
            
            // 绘制当前构件
            ctx.fillStyle = component.color[0];
            ctx.fillRect(canvas.width/2 - 60, 150, 120, 120);
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(component.title, canvas.width/2, 100);
            
            // 特殊动画效果
            if (index === 4) { // 可修改的构件
                // 显示版本替换动画
                setTimeout(() => {
                    ctx.fillStyle = component.color[1];
                    ctx.fillRect(canvas.width/2 - 60, 150, 120, 120);
                    ctx.fillStyle = 'white';
                    ctx.fillText('版本2.0', canvas.width/2, 210);
                }, 1000);
            }
        }

        // 选择答案
        function selectOption(btn, option) {
            const buttons = document.querySelectorAll('.option-btn');
            buttons.forEach(b => {
                b.classList.remove('correct', 'wrong');
                b.disabled = true;
            });
            
            if (option === 'B') {
                btn.classList.add('correct');
                showExplanation(true);
            } else {
                btn.classList.add('wrong');
                // 显示正确答案
                buttons[1].classList.add('correct');
                showExplanation(false);
            }
        }

        // 显示解释
        function showExplanation(isCorrect) {
            const explanation = document.getElementById('explanation');
            
            if (isCorrect) {
                explanation.innerHTML = `
                    <h3>🎉 恭喜答对了！</h3>
                    <p><strong>正确答案：B. 可修改的构件</strong></p>
                    <p>💡 <strong>解题思路：</strong></p>
                    <p>题目关键词是"<strong>版本替换</strong>"和"<strong>增加新功能</strong>"</p>
                    <p>🔍 <strong>分析过程：</strong></p>
                    <p>• 可修改的构件的核心特点就是可以进行版本替换</p>
                    <p>• 通过重新"包装"或写接口来实现构件替换</p>
                    <p>• 能够在原有基础上增加新功能</p>
                    <p>• 在应用系统开发中使用较多</p>
                `;
            } else {
                explanation.innerHTML = `
                    <h3>❌ 答案不正确，让我们一起学习</h3>
                    <p><strong>正确答案：B. 可修改的构件</strong></p>
                    <p>🤔 <strong>为什么不是其他选项：</strong></p>
                    <p>• A. 装配的构件：主要特点是用胶水代码连接</p>
                    <p>• C. 有限制的构件：主要问题是使用时有条件限制</p>
                    <p>• D. 适应性构件：主要特点是解决兼容性问题</p>
                    <p>💡 <strong>记忆技巧：</strong>看到"版本替换"就想到"可修改"！</p>
                `;
            }
            
            gsap.to(explanation, {duration: 0.5, opacity: 1, y: 0, ease: 'back.out(1.7)'});
        }
    </script>
</body>
</html>
